# Account Deletion Documentation

This document describes the account deletion functionality implemented in the Towasl Backend Firebase Functions.

## Overview

The account deletion system implements a **soft delete** approach with automatic permanent deletion after 30 days. This provides users with a grace period to recover their accounts while ensuring data cleanup.

## Flow Diagram

```
Client ──▶ /deleteAccount (set deleted_at and signout)
                │
                ▼
        User account soft-deleted
                │
                ▼
    On verifyOtpAndSignupLogin ──▶ set deleted_at to null (restore account)
                │
                ▼
    deleteAccountPermanently.ts ──▶ scheduled function worker to delete +30 days deleted_at
```

## Functions Structure

```
functions/
├── src/
│   ├── index.ts                   <-- Main entry point
│   ├── config.ts                  <-- Shared configs/env
│   ├── utils/
│   │   └── firestoreHelpers.ts    <-- Reusable helpers
│   └── functions/
│       └── user/
│           ├── createUser.ts      <-- sendOtp and verifyOtpAndSignupLogin
│           ├── deleteAccount.ts   <-- add deleted_at
│           └── deleteAccountPermanently.ts <-- scheduled function to delete +30 days deleted_at
├── tsconfig.json
└── package.json
```

## API Endpoints

### 1. DELETE Account - `/deleteAccount`

**Purpose**: Soft delete a user account by setting `deleted_at` timestamp and signing out the user.

**Method**: `POST`

**Headers**:
- `Content-Type: application/json`
- `X-API-Key: <your-api-key>` (if API key validation is enabled)
- `Authorization: Bearer <firebase-id-token>`

**Request Body**: None

**Response**:
```json
{
  "success": true,
  "message": "Account deleted successfully",
  "deleted_at": *************
}
```

**Error Responses**:
- `401`: Unauthorized (invalid/missing API key or auth token)
- `404`: User not found
- `400`: Account is already deleted
- `500`: Internal server error

**Behavior**:
1. Validates API key and Firebase ID token
2. Sets `deleted_at` timestamp in user document
3. Revokes all refresh tokens (signs out from all devices)
4. Returns success response with deletion timestamp

### 2. Restore Account - `/verifyOtpAndSignupLogin`

**Purpose**: Existing endpoint modified to restore soft-deleted accounts during login.

**Additional Behavior**:
- When a user with `deleted_at` timestamp logs in, the field is set to `null`
- This effectively restores the account and allows normal usage

## Scheduled Function

### `deleteAccountPermanently`

**Schedule**: Daily at 2:00 AM UTC (`0 2 * * *`)

**Purpose**: Permanently delete user accounts that have been soft-deleted for more than 30 days.

**Process**:
1. Finds all users with `deleted_at` timestamp older than 30 days
2. Deletes user document from Firestore
3. Deletes user from Firebase Auth
4. Cleans up related data (rate_limits collection)
5. Logs all operations for audit purposes

**Logging**: All operations are logged with user IDs and timestamps for audit trails.

## Data Model Changes

### User Document Schema

```json
{
  "phoneNumber": "+************",
  "countryCode": "+966",
  "mobile": "*********",
  "duid": "A1B2C3",
  "createdAt": "2023-12-01T10:00:00Z",
  "lastLoginAt": "2023-12-01T10:00:00Z",
  "deleted_at": null | "2023-12-01T15:30:00Z"
}
```

**New Field**:
- `deleted_at`: Timestamp when account was soft-deleted, or `null` if account is active

## Security Rules

Updated Firestore security rules to handle account deletion:

```javascript
// Prevent access to soft-deleted accounts
match /users/{userId} {
  allow read: if request.auth != null 
              && request.auth.uid == userId 
              && (resource == null || resource.data.deleted_at == null);
  
  allow write: if request.auth != null 
               && request.auth.uid == userId 
               && (resource == null || resource.data.deleted_at == null)
               && (
                 // Prevent clients from setting deleted_at (except to null)
                 !request.resource.data.keys().hasAny(['deleted_at']) ||
                 request.resource.data.deleted_at == null
               );
}
```

**Key Security Features**:
1. Soft-deleted accounts cannot be read by clients
2. Clients cannot set `deleted_at` to a timestamp (only Cloud Functions can)
3. Clients can set `deleted_at` to `null` (account restoration via login)
4. Rate limiting and OTP collections remain protected from client access

## Implementation Notes

### Soft Delete Benefits
1. **Grace Period**: Users can recover accounts within 30 days
2. **Data Integrity**: Related data remains consistent during grace period
3. **Audit Trail**: Deletion timestamps provide audit capabilities
4. **Reversible**: Simple restoration process via normal login

### Permanent Deletion Process
1. **Automated**: Runs daily without manual intervention
2. **Comprehensive**: Removes all user data and related records
3. **Logged**: All operations are logged for compliance
4. **Safe**: Uses batch operations and error handling

### Error Handling
- All functions include comprehensive error handling
- Failed operations are logged with context
- Partial failures in scheduled function don't stop other deletions
- Client receives appropriate HTTP status codes and error messages

## Testing Recommendations

1. **Soft Delete Testing**:
   - Verify `deleted_at` timestamp is set correctly
   - Confirm user is signed out from all devices
   - Test that soft-deleted accounts cannot access protected resources

2. **Account Restoration Testing**:
   - Verify soft-deleted accounts can log in normally
   - Confirm `deleted_at` is set to `null` after successful login
   - Test that restored accounts have full functionality

3. **Permanent Deletion Testing**:
   - Test scheduled function with accounts older than 30 days
   - Verify complete removal from both Firestore and Firebase Auth
   - Confirm cleanup of related data (rate_limits)

4. **Security Testing**:
   - Verify Firestore rules prevent access to soft-deleted accounts
   - Test that clients cannot manipulate `deleted_at` field directly
   - Confirm API key and authentication requirements

## Monitoring and Alerts

Consider setting up monitoring for:
- Failed permanent deletion operations
- High volume of account deletions
- Errors in the scheduled function
- Authentication failures on the delete endpoint

## Compliance Considerations

This implementation supports:
- **GDPR Right to be Forgotten**: Permanent deletion after grace period
- **Data Retention Policies**: Configurable retention period (currently 30 days)
- **Audit Requirements**: Comprehensive logging of all deletion operations
- **User Control**: Self-service deletion and restoration capabilities
