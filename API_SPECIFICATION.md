# Towasl Backend API Specification

## Overview

Clean, modern API for SMS OTP authentication with separate country code and mobile number handling.

## Authentication Flow

1. **Send OTP**: User provides country code and mobile number
2. **Receive SMS**: User gets 4-digit OTP via SMS
3. **Verify OTP**: User submits OTP for verification
4. **Get Token**: Backend returns Firebase custom token
5. **Firebase Auth**: Client uses token to authenticate with Firebase

## API Endpoints

### 1. Send OTP

**Endpoint**: `POST /sendOtp`

**Request Body**:
```json
{
  "countryCode": "+966",
  "mobile": "*********"
}
```

**Success Response** (200):
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "expiryTime": 1703123456789
}
```

**Error Responses**:
```json
// Missing country code
{
  "error": "Country code is required"
}

// Missing mobile number
{
  "error": "Mobile number is required"
}

// Invalid phone number format
{
  "error": "Invalid phone number format"
}

// SMS service error
{
  "error": "Failed to send SMS"
}
```

### 2. Verify OTP & Login

**Endpoint**: `POST /verifyOtpAndSignupLogin`

**Request Body**:
```json
{
  "countryCode": "+966",
  "mobile": "*********",
  "otp": "1234"
}
```

**Request Body with Optional Data**:
```json
{
  "countryCode": "+966",
  "mobile": "*********",
  "otp": "1234",
  "userData": {
    "deviceInfo": "iPhone 15 Pro",
    "appVersion": "1.0.0"
  }
}
```

**Success Response** (200):
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "customToken": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "uid": "firebase_user_uid",
    "phoneNumber": "+************",
    "countryCode": "+966",
    "mobile": "*********",
    "duid": "A1B2C3"
  }
}
```

**Error Responses**:
```json
// Missing fields
{
  "error": "Country code is required"
}
{
  "error": "Mobile number is required"
}
{
  "error": "OTP is required"
}

// Invalid OTP
{
  "error": "Invalid OTP"
}

// Expired OTP
{
  "error": "OTP has expired"
}

// OTP not found
{
  "error": "OTP not found or expired"
}

// Already used OTP
{
  "error": "OTP has already been used"
}
```

## 3. Delete Account

**Endpoint**: `POST /deleteAccount`

**Description**: Soft delete user account by setting deleted_at timestamp and signing out user from all devices.

**Headers**:
```
Content-Type: application/json
X-API-Key: <your-api-key>
Authorization: Bearer <firebase-id-token>
```

**Request Body**: None

**Success Response** (200):
```json
{
  "success": true,
  "message": "Account deleted successfully",
  "deleted_at": *************
}
```

**Error Responses**:
```json
// Unauthorized - Invalid/missing API key or auth token
{
  "error": "Unauthorized - Invalid or missing API key"
}

// User not found
{
  "error": "User not found"
}

// Account already deleted
{
  "error": "Account is already deleted"
}
```

## Data Models

### User Document (Firestore)
```json
// Collection: users
// Document ID: {firebase_user_uid}
{
  "phoneNumber": "+************",    // Full phone number
  "countryCode": "+966",             // Country code only
  "mobile": "*********",             // Mobile number only
  "duid": "A1B2C3",                 // 6-char displayed user ID
  "createdAt": "2023-12-01T10:00:00Z",
  "lastLoginAt": "2023-12-01T10:00:00Z",
  "deleted_at": null,                // Soft deletion timestamp (null = active)
  // ... additional user data
}
```

### OTP Request (Firestore - Internal)
```json
// Collection: otp_requests
// Document ID: {full_phone_number}
{
  "otp": "1234",
  "createdAt": "2023-12-01T10:00:00Z",
  "expiryTime": "2023-12-01T10:05:00Z",
  "verified": false
}
```

## Field Specifications

### Country Code
- **Format**: International format with + prefix
- **Examples**: `+966`, `+1`, `+44`, `+971`
- **Validation**: Must start with + and contain 1-4 digits

### Mobile Number
- **Format**: National format without country code
- **Examples**: `*********`, `555123456`
- **Validation**: Must contain only digits, 7-15 characters

### OTP
- **Format**: 4-digit numeric string
- **Example**: `1234`
- **Expiry**: 5 minutes from generation
- **Single Use**: Cannot be reused after verification

### DUID (Displayed User ID)
- **Format**: 6-character alphanumeric (A-Z, 0-9)
- **Example**: `A1B2C3`
- **Uniqueness**: Guaranteed unique across all users
- **Immutable**: Never changes once assigned

## Security Features

### Input Validation
- Phone number format validation
- Required field validation
- Type checking for all inputs

### OTP Security
- 5-minute expiration
- Single-use verification
- Automatic cleanup after use
- Rate limiting (server-side)

### Authentication
- Firebase custom tokens
- Secure session management
- UID-based data access

### Database Security
- Firestore security rules
- Client cannot access OTP collection
- Users can only access their own data

## Error Handling

### HTTP Status Codes
- **200**: Success
- **400**: Bad Request (validation errors)
- **405**: Method Not Allowed
- **500**: Internal Server Error

### Error Response Format
```json
{
  "error": "Human-readable error message"
}
```

## Rate Limiting

### Recommended Limits
- **Send OTP**: 1 request per minute per phone number
- **Verify OTP**: 5 attempts per OTP
- **User Search**: 10 requests per minute per user

## Testing

### Test Endpoints
```bash
# Send OTP
curl -X POST https://us-central1-towasl.cloudfunctions.net/sendOtp \
  -H "Content-Type: application/json" \
  -d '{"countryCode": "+966", "mobile": "*********"}'

# Verify OTP
curl -X POST https://us-central1-towasl.cloudfunctions.net/verifyOtpAndSignupLogin \
  -H "Content-Type: application/json" \
  -d '{"countryCode": "+966", "mobile": "*********", "otp": "1234"}'
```

### Local Testing
```bash
# Start emulator
cd functions && npm run serve

# Test locally
./test-functions.sh http://localhost:5001/towasl/us-central1
```

## Integration Notes

### Flutter Integration
- Use separate input fields for country code and mobile
- Store DUID for user identification
- Implement proper error handling
- Use Firebase Auth with custom tokens

### Country Code Handling
- Default to "+966" for Saudi Arabia
- Implement country code picker
- Validate country codes against known list
- Store separately for better UX

### Mobile Number Handling
- Remove any formatting (spaces, dashes)
- Validate length and format
- Store without country code
- Combine with country code for API calls
