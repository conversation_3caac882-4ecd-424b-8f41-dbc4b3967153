import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import cors from "cors";
import { db, auth, validateA<PERSON><PERSON><PERSON> } from "../../utils/firestoreHelpers";
import { API_CONFIG } from "../../config";

// Initialize CORS with options
const corsHandler = cors({
  origin: functions.config().cors?.allowed_origins?.split(",") || true, // Configurable origins
  methods: ["POST", "OPTIONS"], // Only allow POST and OPTIONS
  allowedHeaders: ["Content-Type", "X-API-Key", "Authorization"],
  credentials: false,
});

/**
 * HTTP Cloud Function to soft delete user account
 * Sets deleted_at timestamp and revokes all refresh tokens (signs out user)
 * Expected request headers: Authorization: Bearer <firebase_id_token>
 */
export const deleteAccount = functions.https.onRequest(async (request, response) => {
  return corsHandler(request, response, async () => {
    try {
      // Validate API key
      if (!validateApiKey(request, API_CONFIG)) {
        response.status(401).json({ error: "Unauthorized - Invalid or missing API key" });
        return;
      }

      // Only allow POST requests
      if (request.method !== "POST") {
        response.status(405).json({ error: "Method not allowed" });
        return;
      }

      // Get the Authorization header
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        response.status(401).json({ error: "Unauthorized - Missing or invalid authorization header" });
        return;
      }

      // Extract the ID token
      const idToken = authHeader.split("Bearer ")[1];

      // Verify the ID token
      let decodedToken;
      try {
        decodedToken = await auth.verifyIdToken(idToken);
      } catch (error) {
        functions.logger.error("Error verifying ID token:", error);
        response.status(401).json({ error: "Unauthorized - Invalid token" });
        return;
      }

      const uid = decodedToken.uid;
      functions.logger.info(`Delete account request for user: ${uid}`);

      // Check if user document exists
      const userDocRef = db.collection("users").doc(uid);
      const userDoc = await userDocRef.get();

      if (!userDoc.exists) {
        response.status(404).json({ error: "User not found" });
        return;
      }

      const userData = userDoc.data();
      
      // Check if user is already soft-deleted
      if (userData?.deleted_at) {
        response.status(400).json({ error: "Account is already deleted" });
        return;
      }

      // Set deleted_at timestamp (soft delete)
      const deletedAt = admin.firestore.Timestamp.now();
      await userDocRef.update({
        deleted_at: deletedAt,
      });

      // Revoke all refresh tokens to sign out the user from all devices
      try {
        await auth.revokeRefreshTokens(uid);
        functions.logger.info(`Revoked refresh tokens for user: ${uid}`);
      } catch (error) {
        functions.logger.error("Error revoking refresh tokens:", error);
        // Continue with the response even if token revocation fails
      }

      functions.logger.info(`User account soft-deleted: ${uid} at ${deletedAt.toDate()}`);

      response.status(200).json({
        success: true,
        message: "Account deleted successfully",
        deleted_at: deletedAt.toMillis(),
      });

    } catch (error) {
      functions.logger.error("Error in deleteAccount function:", error);
      response.status(500).json({ error: "Internal server error" });
    }
  });
});
