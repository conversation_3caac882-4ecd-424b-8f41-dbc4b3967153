import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import { db, auth } from "../../utils/firestoreHelpers";

/**
 * Scheduled Cloud Function to permanently delete user accounts
 * Runs daily at 2:00 AM UTC to delete accounts that have been soft-deleted for more than 30 days
 * This function will:
 * 1. Find all users with deleted_at timestamp older than 30 days
 * 2. Delete the user document from Firestore
 * 3. Delete the user from Firebase Auth
 * 4. Clean up any related data (rate_limits, etc.)
 */
export const deleteAccountPermanently = functions.pubsub
  .schedule("0 2 * * *") // Run daily at 2:00 AM UTC
  .timeZone("UTC")
  .onRun(async (context) => {
    try {
      functions.logger.info("Starting permanent account deletion job");

      // Calculate the cutoff date (30 days ago)
      const thirtyDaysAgo = admin.firestore.Timestamp.fromMillis(
        Date.now() - (30 * 24 * 60 * 60 * 1000) // 30 days in milliseconds
      );

      // Find all users marked for deletion more than 30 days ago
      const usersToDelete = await db.collection("users")
        .where("deleted_at", "<=", thirtyDaysAgo)
        .where("deleted_at", "!=", null)
        .get();

      if (usersToDelete.empty) {
        functions.logger.info("No accounts found for permanent deletion");
        return null;
      }

      functions.logger.info(`Found ${usersToDelete.size} accounts for permanent deletion`);

      const batch = db.batch();
      const deletionPromises: Promise<void>[] = [];
      const deletedUsers: string[] = [];

      for (const userDoc of usersToDelete.docs) {
        const uid = userDoc.id;
        const userData = userDoc.data();
        
        functions.logger.info(`Processing permanent deletion for user: ${uid}, deleted_at: ${userData.deleted_at?.toDate()}`);

        try {
          // Delete user from Firebase Auth
          deletionPromises.push(
            auth.deleteUser(uid).then(() => {
              functions.logger.info(`Deleted Firebase Auth user: ${uid}`);
            }).catch((error) => {
              functions.logger.error(`Failed to delete Firebase Auth user ${uid}:`, error);
              // Continue with Firestore deletion even if Auth deletion fails
            })
          );

          // Add user document deletion to batch
          batch.delete(userDoc.ref);

          // Clean up rate limiting records for this user's phone number
          if (userData.phoneNumber) {
            const rateLimitQuery = await db.collection("rate_limits")
              .where("phoneNumber", "==", userData.phoneNumber)
              .get();

            rateLimitQuery.docs.forEach((rateLimitDoc) => {
              batch.delete(rateLimitDoc.ref);
            });
          }

          deletedUsers.push(uid);

        } catch (error) {
          functions.logger.error(`Error processing user ${uid} for permanent deletion:`, error);
        }
      }

      // Execute all Firebase Auth deletions
      await Promise.allSettled(deletionPromises);

      // Execute Firestore batch deletion
      await batch.commit();

      functions.logger.info(`Permanent deletion job completed. Deleted ${deletedUsers.length} accounts: ${deletedUsers.join(", ")}`);

      return {
        success: true,
        deletedCount: deletedUsers.length,
        deletedUsers: deletedUsers,
      };

    } catch (error) {
      functions.logger.error("Error in deleteAccountPermanently function:", error);
      throw error; // Re-throw to mark the function as failed
    }
  });
