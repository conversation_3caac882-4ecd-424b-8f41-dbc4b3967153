import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import cors from "cors";
import { 
  db, 
  auth, 
  generateOTP, 
  generateUniqueDUID, 
  isValidPhoneNumber, 
  validateApi<PERSON>ey, 
  checkPhoneRateLimit, 
  checkIPRateLimit, 
  recordRateLimit, 
  sendSMS, 
  getClientIP 
} from "../../utils/firestoreHelpers";
import { MSEGAT_CONFIG, API_CONFIG } from "../../config";

// Initialize CORS with options
const corsHandler = cors({
  origin: functions.config().cors?.allowed_origins?.split(",") || true, // Configurable origins
  methods: ["POST", "OPTIONS"], // Only allow POST and OPTIONS
  allowedHeaders: ["Content-Type", "X-API-Key"],
  credentials: false,
});

/**
 * HTTP Cloud Function to send OTP via SMS
 * Expected request body: { countryCode: string, mobile: string }
 */
export const sendOtp = functions.https.onRequest(async (request, response) => {
  return corsHandler(request, response, async () => {
    try {
      // Validate API key
      if (!validateApiKey(request, API_CONFIG)) {
        response.status(401).json({ error: "Unauthorized - Invalid or missing API key" });
        return;
      }

      // Check if required configuration is available
      if (!MSEGAT_CONFIG.username || !MSEGAT_CONFIG.apiKey) {
        functions.logger.error("Msegat configuration missing");
        response.status(500).json({ error: "SMS service configuration error" });
        return;
      }

      // Only allow POST requests
      if (request.method !== "POST") {
        response.status(405).json({ error: "Method not allowed" });
        return;
      }

      const { countryCode, mobile } = request.body;

      // Validate required fields
      if (!countryCode || typeof countryCode !== "string") {
        response.status(400).json({ error: "Country code is required" });
        return;
      }

      if (!mobile || typeof mobile !== "string") {
        response.status(400).json({ error: "Mobile number is required" });
        return;
      }

      // Combine country code and mobile number
      const fullPhoneNumber = countryCode + mobile;

      // Validate phone number format
      if (!isValidPhoneNumber(fullPhoneNumber)) {
        response.status(400).json({ error: "Invalid phone number format" });
        return;
      }

      // Get client IP for rate limiting
      const clientIP = getClientIP(request);

      // Check rate limits
      const phoneRateLimitOk = await checkPhoneRateLimit(fullPhoneNumber);
      const ipRateLimitOk = await checkIPRateLimit(clientIP);

      if (!phoneRateLimitOk) {
        functions.logger.warn(`Rate limit exceeded for phone: ${fullPhoneNumber}`);
        response.status(429).json({
          error: "Too many OTP requests for this phone number. Please wait before trying again."
        });
        return;
      }

      if (!ipRateLimitOk) {
        functions.logger.warn(`Rate limit exceeded for IP: ${clientIP}`);
        response.status(429).json({
          error: "Too many OTP requests from this location. Please wait before trying again."
        });
        return;
      }

      // Record rate limit attempt
      await recordRateLimit(fullPhoneNumber, clientIP as string, "otp_request");

      // Generate OTP
      const otp = generateOTP();
      const now = admin.firestore.Timestamp.now();
      const expiryTime = admin.firestore.Timestamp.fromMillis(
        now.toMillis() + (5 * 60 * 1000) // 5 minutes expiry
      );

      // Store OTP in Firestore using full phone number as document ID
      await db.collection("otp_requests").doc(fullPhoneNumber).set({
        otp: otp,
        createdAt: now,
        expiryTime: expiryTime,
        verified: false,
        ipAddress: clientIP, // Track IP for security monitoring
      });

      // Prepare SMS message
      const smsMessage = MSEGAT_CONFIG.messageTemplate.replace("xxxx", otp);

      // Send SMS
      const smsSent = await sendSMS(fullPhoneNumber, smsMessage);

      if (!smsSent) {
        response.status(500).json({ error: "Failed to send SMS" });
        return;
      }

      functions.logger.info(`OTP sent successfully to ${fullPhoneNumber} from IP: ${clientIP}`);

      response.status(200).json({
        success: true,
        message: "OTP sent successfully",
        expiryTime: expiryTime.toMillis(),
      });

    } catch (error) {
      functions.logger.error("Error in sendOtp function:", error);
      response.status(500).json({ error: "Internal server error" });
    }
  });
});

/**
 * HTTP Cloud Function to verify OTP and handle user signup/login
 * Expected request body: { countryCode: string, mobile: string, otp: string, userData?: object }
 */
export const verifyOtpAndSignupLogin = functions.https.onRequest(async (request, response) => {
  return corsHandler(request, response, async () => {
    try {
      // Validate API key
      if (!validateApiKey(request, API_CONFIG)) {
        response.status(401).json({ error: "Unauthorized - Invalid or missing API key" });
        return;
      }

      // Only allow POST requests
      if (request.method !== "POST") {
        response.status(405).json({ error: "Method not allowed" });
        return;
      }

      const { countryCode, mobile, otp, userData } = request.body;

      // Validate required fields
      if (!countryCode || typeof countryCode !== "string") {
        response.status(400).json({ error: "Country code is required" });
        return;
      }

      if (!mobile || typeof mobile !== "string") {
        response.status(400).json({ error: "Mobile number is required" });
        return;
      }

      // Combine country code and mobile number
      const fullPhoneNumber = countryCode + mobile;

      // Validate input
      if (!otp || typeof otp !== "string") {
        response.status(400).json({ error: "OTP is required" });
        return;
      }

      if (!isValidPhoneNumber(fullPhoneNumber)) {
        response.status(400).json({ error: "Invalid phone number format" });
        return;
      }

      // Retrieve OTP record from Firestore using full phone number
      const otpDoc = await db.collection("otp_requests").doc(fullPhoneNumber).get();

      if (!otpDoc.exists) {
        response.status(400).json({ error: "OTP not found or expired" });
        return;
      }

      const otpData = otpDoc.data();
      if (!otpData) {
        response.status(400).json({ error: "Invalid OTP data" });
        return;
      }

      // Check if OTP has expired
      const now = admin.firestore.Timestamp.now();
      if (now.toMillis() > otpData.expiryTime.toMillis()) {
        // Clean up expired OTP
        await db.collection("otp_requests").doc(fullPhoneNumber).delete();
        response.status(400).json({ error: "OTP has expired" });
        return;
      }

      // Verify OTP
      if (otpData.otp !== otp) {
        response.status(400).json({ error: "Invalid OTP" });
        return;
      }

      // Check if OTP has already been verified
      if (otpData.verified) {
        response.status(400).json({ error: "OTP has already been used" });
        return;
      }

      // Mark OTP as verified
      await db.collection("otp_requests").doc(fullPhoneNumber).update({
        verified: true,
      });

      // Check if user already exists in Firebase Auth
      let userRecord;
      try {
        // Try to get user by phone number
        const userByPhone = await auth.getUserByPhoneNumber(fullPhoneNumber);
        userRecord = userByPhone;
        functions.logger.info(`Existing user found: ${userRecord.uid}`);
      } catch (error) {
        // User doesn't exist, create new user
        functions.logger.info("Creating new user for phone:", fullPhoneNumber);
        userRecord = await auth.createUser({
          phoneNumber: fullPhoneNumber,
          disabled: false,
        });
        functions.logger.info(`New user created: ${userRecord.uid}`);
      }

      // Create or update user document in Firestore
      const userDocRef = db.collection("users").doc(userRecord.uid);
      const userDoc = await userDocRef.get();

      let userDUID: string;

      if (!userDoc.exists) {
        // Generate unique DUID for new user
        userDUID = await generateUniqueDUID();

        const defaultUserData = {
          phoneNumber: fullPhoneNumber,
          countryCode: countryCode,
          mobile: mobile,
          duid: userDUID,
          createdAt: admin.firestore.Timestamp.now(),
          lastLoginAt: admin.firestore.Timestamp.now(),
          deleted_at: null, // Initialize as not deleted
        };

        // Create new user document
        await userDocRef.set({
          ...defaultUserData,
          ...userData,
        });
        functions.logger.info(`User document created for UID: ${userRecord.uid} with DUID: ${userDUID}`);
      } else {
        // Get existing DUID or generate new one if missing
        const existingData = userDoc.data();
        userDUID = existingData?.duid;

        if (!userDUID) {
          // Generate DUID for existing user who doesn't have one
          userDUID = await generateUniqueDUID();
          functions.logger.info(`Generated DUID for existing user: ${userRecord.uid} - DUID: ${userDUID}`);
        }

        // Update existing user document and restore account if it was soft-deleted
        await userDocRef.update({
          lastLoginAt: admin.firestore.Timestamp.now(),
          countryCode: countryCode,
          mobile: mobile,
          duid: userDUID, // Ensure DUID is always present
          deleted_at: null, // Restore soft-deleted account
          ...(userData && userData),
        });
        functions.logger.info(`User document updated for UID: ${userRecord.uid}`);
      }

      // Create custom token
      const customToken = await auth.createCustomToken(userRecord.uid);

      // Clean up the OTP record after successful verification
      await db.collection("otp_requests").doc(fullPhoneNumber).delete();

      functions.logger.info(`Custom token created for user: ${userRecord.uid}`);

      response.status(200).json({
        success: true,
        message: "OTP verified successfully",
        customToken: customToken,
        user: {
          uid: userRecord.uid,
          phoneNumber: userRecord.phoneNumber,
          countryCode: countryCode,
          mobile: mobile,
          duid: userDUID,
        },
      });

    } catch (error) {
      functions.logger.error("Error in verifyOtpAndSignupLogin function:", error);
      response.status(500).json({ error: "Internal server error" });
    }
  });
});
