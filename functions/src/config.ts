import * as functions from "firebase-functions";

// Msegat configuration from environment variables
export const MSEGAT_CONFIG = {
  username: functions.config().msegat?.username,
  apiKey: functions.config().msegat?.api_key,
  senderId: functions.config().msegat?.sender_id || "auth-mseg",
  messageTemplate: functions.config().msegat?.message_template || "Pin Code is: xxxx",
  baseUrl: "https://www.msegat.com/gw/sendsms.php",
};

// API Security configuration
export const API_CONFIG = {
  apiKey: functions.config().api?.key,
  requireApiKey: functions.config().api?.require_key === "true",
};

// Rate limiting configuration
export const RATE_LIMITS = {
  OTP_PER_PHONE_PER_MINUTE: 1,
  OTP_PER_IP_PER_HOUR: 10,
  VERIFY_ATTEMPTS_PER_OTP: 5,
};

// Validate required environment variables
if (!MSEGAT_CONFIG.username || !MSEGAT_CONFIG.apiKey) {
  functions.logger.error("Missing required Msegat configuration. Please set msegat.username and msegat.api_key using Firebase Functions config.");
}
